<template>
  <!-- {{ AURA-X: Modify - 完全响应式重构，重新设计布局结构. Approval: 寸止(ID:1735374000). }} -->
  <view class="login-page">
    <!-- 背景装饰层 -->
    <view class="login-background">
      <view class="bg-decoration bg-decoration-1"></view>
      <view class="bg-decoration bg-decoration-2"></view>
      <view class="bg-decoration bg-decoration-3"></view>
    </view>

    <!-- 主要内容区域 -->
    <view class="login-main">
      <!-- 登录卡片 -->
      <view class="login-card">
        <!-- 头部区域 -->
        <view class="login-header">
          <text class="login-title">登录</text>
        </view>

        <!-- 表单区域 -->
        <view class="login-form">
          <view class="form-item">
            <view class="form-label">
              <uv-icon name="phone" color="#666" label="手机号"></uv-icon>
            </view>
            <uv-input
              v-model="form.mobile"
              placeholder="请输入手机号"
              type="number"
              maxlength="11"
              border="false"
              :customStyle="inputStyle"
            />
          </view>

          <view class="form-item">
            <view class="form-label">
              <uv-icon name="lock" color="#666" label="密码"></uv-icon>
            </view>
            <uv-input
              v-model="form.password"
              placeholder="请输入密码"
              :password="!showPassword"
              border="false"
              :customStyle="inputStyle"
            >
            <template #suffix>
              <uv-icon
                :name="showPassword ? 'eye-off' : 'eye'"
                color="#666"
                @click="togglePassword"
                class="password-toggle"
              ></uv-icon>
            </template>
            </uv-input>
          </view>

          <view class="login-actions">
            <uv-button
              type="primary"
              :loading="loading"
              :disabled="!canLogin"
              @click="handleLogin"
              :customStyle="buttonStyle"
              :text="loading ? '登录中...' : '登录'"
            >
            </uv-button>
          </view>
        </view>

        <!-- 底部信息 -->
        <view class="login-footer">
          <text class="footer-text">如有问题请联系管理员</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
// {{ AURA-X: Add - 引入公共工具函数，优化代码结构. Approval: 寸止(ID:1735374000). }}
import utils from '@/common/js/utils.js'

export default {
  data() {
    return {
      form: {
        mobile: '',
        password: ''
      },
      showPassword: false,
      loading: false
    }
  },
  computed: {
    canLogin() {
      // {{ AURA-X: Modify - 使用公共验证函数和常量，提高验证准确性. Approval: 寸止(ID:1735374000). }}
      return utils.validateMobile(this.form.mobile) &&
             this.form.password.length >= utils.getConstant('MIN_PASSWORD_LENGTH')
    },
    // {{ AURA-X: Add - 响应式输入框样式. Approval: 寸止(ID:1735374000). }}
    inputStyle() {
      const baseStyle = {
        backgroundColor: '#f8f9fa',
        border: '1rpx solid #e9ecef',
        transition: 'all 0.3s ease'
      }

      // #ifdef H5
      if (typeof window !== 'undefined' && window.innerWidth >= 1024) {
        return {
          ...baseStyle,
          borderRadius: '8px',
          padding: '12px 16px',
          fontSize: '16px',
          lineHeight: '1.5',
          border: '1px solid #e9ecef'
        }
      }
      // #endif

      return {
        ...baseStyle,
        borderRadius: '12rpx',
        padding: '24rpx 32rpx',
        fontSize: '28rpx'
      }
    },
    // {{ AURA-X: Add - 响应式按钮样式. Approval: 寸止(ID:1735374000). }}
    buttonStyle() {
      const baseStyle = {
        fontWeight: '500',
        transition: 'all 0.3s ease'
      }

      // #ifdef H5
      if (typeof window !== 'undefined' && window.innerWidth >= 1024) {
        return {
          ...baseStyle,
          borderRadius: '8px',
          height: '48px',
          fontSize: '16px'
        }
      }
      // #endif

      return {
        ...baseStyle,
        borderRadius: '12rpx',
        height: '88rpx',
        fontSize: '28rpx'
      }
    }
  },
  methods: {
    togglePassword() {
      this.showPassword = !this.showPassword
    },
    
    async handleLogin() {
      if (!this.canLogin) {
        // {{ AURA-X: Modify - 使用公共错误提示函数. Approval: 寸止(ID:1735374000). }}
        utils.showError('请输入正确的手机号和密码')
        return
      }
      
      this.loading = true
      
      try {
        const result = await uniCloud.callFunction({
          name: 'user-auth',
          data: {
            action: 'login',
            data: {
              mobile: this.form.mobile,
              password: this.form.password
            }
          }
        })
        
        if (result.result.code === 0) {
          // 登录成功，保存token和用户信息
          const { token, userInfo } = result.result.data
          
          // 保存到本地存储
          uni.setStorageSync('token', token)
          uni.setStorageSync('userInfo', userInfo)

          // {{ AURA-X: Modify - 使用公共成功提示函数. Approval: 寸止(ID:1735374000). }}
          utils.showSuccess('登录成功')

          // 跳转到首页
          setTimeout(() => {
            uni.reLaunch({
              url: '/pages/shelf-monitor/index'
            })
          }, 1500)
        } else {
          // {{ AURA-X: Modify - 使用公共错误提示函数. Approval: 寸止(ID:1735374000). }}
          utils.showError(result.result.message || '登录失败')
        }
      } catch (error) {
        console.error('登录失败:', error)
        // {{ AURA-X: Modify - 使用公共错误提示函数. Approval: 寸止(ID:1735374000). }}
        utils.showError('网络错误，请重试')
      } finally {
        this.loading = false
      }
    }
  },
  
  onLoad() {
    // 检查是否已经登录
    const token = uni.getStorageSync('token')
    if (token) {
      uni.reLaunch({
        url: '/pages/shelf-monitor/index'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
/* {{ AURA-X: Modify - 完全响应式重构，使用项目响应式系统. Approval: 寸止(ID:1735374000). }} */

/* 页面容器 - 使用flexbox布局 */
.login-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: $primary-gradient;
  position: relative;
  overflow: hidden;
  @include responsive-spacing(padding, base);

  // PC端优化
  @include desktop-up {
    padding: $spacing-xl-pc;
  }
}

/* 背景装饰元素 */
.login-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 0;
}

.bg-decoration {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);

  &.bg-decoration-1 {
    width: 200rpx;
    height: 200rpx;
    top: 10%;
    right: 10%;
    animation: float 6s ease-in-out infinite;

    @include desktop-up {
      width: 120px;
      height: 120px;
    }
  }

  &.bg-decoration-2 {
    width: 150rpx;
    height: 150rpx;
    bottom: 20%;
    left: 5%;
    animation: float 8s ease-in-out infinite reverse;

    @include desktop-up {
      width: 80px;
      height: 80px;
    }
  }

  &.bg-decoration-3 {
    width: 100rpx;
    height: 100rpx;
    top: 30%;
    left: 15%;
    animation: float 10s ease-in-out infinite;

    @include desktop-up {
      width: 60px;
      height: 60px;
    }
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

/* 主要内容区域 */
.login-main {
  width: 100%;
  max-width: 100%;
  z-index: 1;
  position: relative;

  // 移动端
  @include mobile-only {
    max-width: 100%;
  }

  // 平板端
  @include tablet-up {
    max-width: 480px;
  }

  // PC端
  @include desktop-up {
    max-width: 420px;
  }
}

/* 登录卡片 */
.login-card {
  background: $bg-color-container;
  border-radius: $border-radius-xl;
  box-shadow: $shadow-lg;
  overflow: hidden;
  backdrop-filter: blur(10px);
  border: 1rpx solid rgba(255, 255, 255, 0.2);

  // 移动端
  @include mobile-only {
    margin: $spacing-base;
    border-radius: $border-radius-lg;
  }

  // PC端
  @include desktop-up {
    border-radius: 16px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }
}

/* 头部区域 */
.login-header {
  text-align: center;
  @include responsive-spacing(padding, xl);
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);

  @include desktop-up {
    padding: $spacing-xl-pc $spacing-xl-pc $spacing-lg-pc;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  }
}

.login-title {
  @include responsive-font(xxl);
  font-weight: $font-weight-semibold;
  color: $text-color-primary;
}

/* 表单区域 */
.login-form {
  @include responsive-spacing(padding, xl);

  @include desktop-up {
    padding: $spacing-lg-pc $spacing-xl-pc;
  }
}

.form-item {
  @include responsive-spacing(margin-bottom, lg);

  &:last-child {
    margin-bottom: 0;
  }
}

.form-label {
  display: flex;
  align-items: center;
  @include responsive-spacing(margin-bottom, sm);
  gap: $spacing-xs;

  @include desktop-up {
    gap: $spacing-xs-pc;
    margin-bottom: $spacing-sm-pc;
  }
}

.password-toggle {
  cursor: pointer;
  transition: all $transition-fast;
  padding: $spacing-xs;
  border-radius: $border-radius-sm;

  &:hover {
    @include desktop-up {
      background: rgba(0, 0, 0, 0.05);
    }
  }

  &:active {
    opacity: 0.7;
  }
}

/* 输入框样式增强 */
:deep(.uv-input) {
  transition: all $transition-base;

  &:focus-within {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(60, 156, 255, 0.15);
  }
}

.login-actions {
  @include responsive-spacing(margin-top, xl);

  @include desktop-up {
    margin-top: $spacing-xl-pc;
  }
}

/* 按钮样式增强 */
:deep(.uv-button) {
  width: 100%;
  transition: all $transition-base;

  &:not(.uv-button--disabled):hover {
    @include desktop-up {
      transform: translateY(-2px);
      box-shadow: 0 8px 20px rgba(60, 156, 255, 0.3);
    }
  }

  &:not(.uv-button--disabled):active {
    transform: translateY(0);
  }
}

/* 底部区域 */
.login-footer {
  text-align: center;
  @include responsive-spacing(padding, lg);
  background: rgba(0, 0, 0, 0.02);
  border-top: 1rpx solid rgba(0, 0, 0, 0.05);

  @include desktop-up {
    padding: $spacing-lg-pc;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
  }
}

.footer-text {
  @include responsive-font(sm);
  color: $text-color-placeholder;
  line-height: 1.5;
}

/* 响应式优化 */
@include mobile-only {
  .login-page {
    align-items: flex-start;
    padding-top: 10vh;
  }

  .login-card {
    min-height: auto;
  }
}

@include tablet-up {
  .login-page {
    align-items: center;
  }
}

/* 无障碍和交互优化 */
.focusable {
  &:focus {
    outline: 2px solid $primary-color;
    outline-offset: 2px;
  }
}

/* 加载状态优化 */
.login-card {
  &.loading {
    pointer-events: none;
    opacity: 0.8;
  }
}

/* 深色模式支持预留 */
@media (prefers-color-scheme: dark) {
  .login-card {
    background: rgba(255, 255, 255, 0.95);
  }

  .bg-decoration {
    background: rgba(255, 255, 255, 0.08);
  }
}
</style>